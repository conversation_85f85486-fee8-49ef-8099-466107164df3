<footer class="relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden">
  <!-- Decorative Top Border -->
  <div class="h-1 bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500"></div>

  <!-- <PERSON><PERSON><PERSON> Background -->
  <div class="absolute inset-0 mithila-peacock-pattern opacity-10 pointer-events-none"></div>

  <!-- Floating Decorative Elements -->
  <div class="absolute top-10 left-10 w-20 h-20 opacity-20">
    <svg viewBox="0 0 100 100" class="w-full h-full text-primary-400 animate-float-slow">
      <circle cx="50" cy="50" r="30" fill="none" stroke="currentColor" stroke-width="2"/>
      <path d="M30 50 Q50 30 70 50 Q50 70 30 50" fill="currentColor" opacity="0.3"/>
    </svg>
  </div>
  <div class="absolute bottom-20 right-20 w-16 h-16 opacity-20">
    <svg viewBox="0 0 100 100" class="w-full h-full text-secondary-400 animate-float-medium">
      <polygon points="50,10 90,90 10,90" fill="none" stroke="currentColor" stroke-width="2"/>
      <circle cx="50" cy="60" r="15" fill="currentColor" opacity="0.3"/>
    </svg>
  </div>

  <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-12">
      <!-- Enhanced Logo and About Section -->
      <div class="col-span-1 md:col-span-1">
        <div class="flex items-center mb-6 group">
          <div class="relative">
            <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center shadow-lg transform transition-all duration-300 group-hover:scale-110">
              <span class="text-white font-bold text-lg font-display">M</span>
            </div>
            <div class="absolute -inset-1 border-2 border-primary-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
          <div class="ml-4">
            <span class="text-2xl font-display font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Mithilani Ghar
            </span>
            <p class="text-xs font-handwritten text-primary-300 -mt-1">Traditional Art & Crafts</p>
          </div>
        </div>
        <p class="text-gray-300 mb-6 leading-relaxed">
          Preserving and promoting the rich artistic heritage of Mithila region through traditional arts, crafts, and community engagement. Connecting artists with art lovers worldwide.
        </p>
        <div class="flex space-x-4">
          <a href="https://facebook.com" target="_blank" class="group relative p-3 bg-gray-800 hover:bg-primary-600 rounded-full transition-all duration-300 transform hover:scale-110 hover:shadow-lg">
            <span class="sr-only">Facebook</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
            </svg>
          </a>
          <a href="https://instagram.com" target="_blank" class="group relative p-3 bg-gray-800 hover:bg-gradient-to-r hover:from-purple-600 hover:to-pink-600 rounded-full transition-all duration-300 transform hover:scale-110 hover:shadow-lg">
            <span class="sr-only">Instagram</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd" />
            </svg>
          </a>
          <a href="https://youtube.com" target="_blank" class="group relative p-3 bg-gray-800 hover:bg-red-600 rounded-full transition-all duration-300 transform hover:scale-110 hover:shadow-lg">
            <span class="sr-only">YouTube</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fill-rule="evenodd" d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z" clip-rule="evenodd" />
            </svg>
          </a>
          <a href="https://twitter.com" target="_blank" class="group relative p-3 bg-gray-800 hover:bg-blue-500 rounded-full transition-all duration-300 transform hover:scale-110 hover:shadow-lg">
            <span class="sr-only">Twitter</span>
            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
            </svg>
          </a>
        </div>
      </div>

      <!-- Enhanced Quick Links -->
      <div class="col-span-1">
        <h3 class="text-xl font-display font-bold mb-6 bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
          Quick Links
        </h3>
        <ul class="space-y-3">
          <li>
            <a routerLink="/" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
              <span class="w-2 h-2 bg-primary-500 rounded-full mr-3 transform group-hover:scale-125 transition-transform duration-300"></span>
              Home
            </a>
          </li>
          <li>
            <a routerLink="/about" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
              <span class="w-2 h-2 bg-primary-500 rounded-full mr-3 transform group-hover:scale-125 transition-transform duration-300"></span>
              About Us
            </a>
          </li>
          <li>
            <a routerLink="/gallery" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
              <span class="w-2 h-2 bg-primary-500 rounded-full mr-3 transform group-hover:scale-125 transition-transform duration-300"></span>
              Gallery
            </a>
          </li>
          <li>
            <a routerLink="/artists" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
              <span class="w-2 h-2 bg-primary-500 rounded-full mr-3 transform group-hover:scale-125 transition-transform duration-300"></span>
              Artists
            </a>
          </li>
          <li>
            <a routerLink="/products" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
              <span class="w-2 h-2 bg-primary-500 rounded-full mr-3 transform group-hover:scale-125 transition-transform duration-300"></span>
              Products
            </a>
          </li>
          <li>
            <a routerLink="/contact" class="group flex items-center text-gray-300 hover:text-white transition-all duration-300">
              <span class="w-2 h-2 bg-primary-500 rounded-full mr-3 transform group-hover:scale-125 transition-transform duration-300"></span>
              Contact
            </a>
          </li>
        </ul>
      </div>

      <!-- Enhanced Contact Info -->
      <div class="col-span-1">
        <h3 class="text-xl font-display font-bold mb-6 bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
          Contact Us
        </h3>
        <ul class="space-y-4">
          <li class="group flex items-start hover:bg-gray-800/50 p-3 rounded-lg transition-all duration-300">
            <div class="p-2 bg-primary-600 rounded-full mr-4 group-hover:bg-primary-500 transition-colors duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <span class="text-gray-300 text-sm leading-relaxed">Barahbigha, Janaki Mandir Marg, Janakpurdham-08, Dhanusha, Nepal</span>
          </li>
          <li class="group flex items-start hover:bg-gray-800/50 p-3 rounded-lg transition-all duration-300">
            <div class="p-2 bg-secondary-600 rounded-full mr-4 group-hover:bg-secondary-500 transition-colors duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            </div>
            <span class="text-gray-300 text-sm">+977-9814830580 / +977-9821762884</span>
          </li>
          <li class="group flex items-start hover:bg-gray-800/50 p-3 rounded-lg transition-all duration-300">
            <div class="p-2 bg-accent-600 rounded-full mr-4 group-hover:bg-accent-500 transition-colors duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <span class="text-gray-300 text-sm">mithilanighar&#64;gmail.com</span>
          </li>
          <li class="group flex items-start hover:bg-gray-800/50 p-3 rounded-lg transition-all duration-300">
            <div class="p-2 bg-success-600 rounded-full mr-4 group-hover:bg-success-500 transition-colors duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <span class="text-gray-300 text-sm">Open daily: 9:00 AM - 8:00 PM</span>
          </li>
        </ul>
      </div>

      <!-- Enhanced Newsletter Section -->
      <div class="col-span-1">
        <h3 class="text-xl font-display font-bold mb-6 bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
          Stay Connected
        </h3>
        <p class="text-gray-300 mb-6 leading-relaxed">Subscribe to our newsletter for updates on new artworks, cultural events, and exclusive promotions from Mithila artists.</p>
        <form class="space-y-4">
          <div class="relative">
            <label for="email" class="sr-only">Email address</label>
            <input type="email" id="email" name="email" placeholder="Enter your email address" required
              class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 backdrop-blur-sm">
            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
              </svg>
            </div>
          </div>
          <button type="submit" class="w-full btn-mithila-primary text-center">
            Subscribe to Newsletter
          </button>
        </form>

        <!-- Cultural Quote -->
        <div class="mt-8 p-4 bg-gradient-to-r from-primary-900/20 to-secondary-900/20 rounded-lg border border-primary-800/30">
          <p class="text-primary-300 font-handwritten text-sm italic text-center">
            "Art is the bridge between cultures, connecting hearts across the world."
          </p>
        </div>
      </div>
    </div>

    <!-- Enhanced Bottom Footer -->
    <div class="relative mt-16">
      <!-- Decorative Border -->
      <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary-500 to-transparent"></div>

      <div class="pt-8 flex flex-col md:flex-row justify-between items-center">
        <div class="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
          <p class="text-gray-400 text-sm">© {{currentYear}} Mithilani Ghar. All rights reserved.</p>
          <p class="text-gray-500 text-xs">Preserving Mithila Heritage Since 2015</p>
        </div>
        <div class="mt-4 md:mt-0 flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
          <ul class="flex space-x-6 text-sm">
            <li><a href="#" class="text-gray-400 hover:text-primary-400 transition-colors duration-300">Privacy Policy</a></li>
            <li><a href="#" class="text-gray-400 hover:text-primary-400 transition-colors duration-300">Terms of Service</a></li>
            <li><a href="#" class="text-gray-400 hover:text-primary-400 transition-colors duration-300">Shipping Info</a></li>
          </ul>
          <div class="flex items-center space-x-2 text-xs text-gray-500">
            <span>Made with</span>
            <svg class="h-4 w-4 text-red-500 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"/>
            </svg>
            <span>in Nepal</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</footer>
