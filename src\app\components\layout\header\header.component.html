<header class="relative bg-white/95 backdrop-blur-md shadow-lg border-b-2 border-primary-200">
  <!-- Decorative Top Border -->
  <div class="h-1 bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500"></div>

  <!-- <PERSON><PERSON><PERSON> Background -->
  <div class="absolute inset-0 mithila-lotus-pattern opacity-30 pointer-events-none"></div>

  <div class="container mx-auto px-4 relative z-10">
    <div class="flex justify-between items-center py-4">
      <!-- Enhanced Logo with Artistic Elements -->
      <a routerLink="/" class="flex items-center space-x-4 group">
        <div class="relative">
          <!-- Main Logo Circle -->
          <div class="w-14 h-14 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center shadow-lg transform transition-all duration-300 group-hover:scale-110 group-hover:rotate-12">
            <span class="text-white font-bold text-xl font-display">M</span>
          </div>
          <!-- Decorative Ring -->
          <div class="absolute -inset-1 border-2 border-primary-300 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-ping-slow"></div>
          <!-- Corner Decorations -->
          <div class="absolute -top-1 -left-1 w-3 h-3 border-t-2 border-l-2 border-accent-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="absolute -bottom-1 -right-1 w-3 h-3 border-b-2 border-r-2 border-accent-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </div>
        <div class="flex flex-col">
          <span class="text-2xl font-bold font-display bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
            Mithilani Ghar
          </span>
          <span class="text-xs font-handwritten text-primary-500 -mt-1 opacity-80">
            Traditional Art & Crafts
          </span>
        </div>
      </a>

      <!-- Enhanced Desktop Navigation with Artistic Elements -->
      <nav class="hidden md:flex items-center space-x-1">
        <a routerLink="/" routerLinkActive="text-primary-600 bg-primary-50" [routerLinkActiveOptions]="{exact: true}"
          class="relative px-4 py-2 text-gray-700 hover:text-primary-600 transition-all duration-300 font-medium rounded-lg group">
          <span class="relative z-10">Home</span>
          <div class="absolute inset-0 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="absolute bottom-0 left-1/2 w-0 h-0.5 bg-primary-500 group-hover:w-full group-hover:left-0 transition-all duration-300"></div>
        </a>
        <a routerLink="/products" routerLinkActive="text-primary-600 bg-primary-50"
          class="relative px-4 py-2 text-gray-700 hover:text-primary-600 transition-all duration-300 font-medium rounded-lg group">
          <span class="relative z-10">Products</span>
          <div class="absolute inset-0 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="absolute bottom-0 left-1/2 w-0 h-0.5 bg-primary-500 group-hover:w-full group-hover:left-0 transition-all duration-300"></div>
        </a>
        <a routerLink="/gallery" routerLinkActive="text-primary-600 bg-primary-50"
          class="relative px-4 py-2 text-gray-700 hover:text-primary-600 transition-all duration-300 font-medium rounded-lg group">
          <span class="relative z-10">Gallery</span>
          <div class="absolute inset-0 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="absolute bottom-0 left-1/2 w-0 h-0.5 bg-primary-500 group-hover:w-full group-hover:left-0 transition-all duration-300"></div>
        </a>
        <a routerLink="/artists" routerLinkActive="text-primary-600 bg-primary-50"
          class="relative px-4 py-2 text-gray-700 hover:text-primary-600 transition-all duration-300 font-medium rounded-lg group">
          <span class="relative z-10">Artists</span>
          <div class="absolute inset-0 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="absolute bottom-0 left-1/2 w-0 h-0.5 bg-primary-500 group-hover:w-full group-hover:left-0 transition-all duration-300"></div>
        </a>
        <a routerLink="/about" routerLinkActive="text-primary-600 bg-primary-50"
          class="relative px-4 py-2 text-gray-700 hover:text-primary-600 transition-all duration-300 font-medium rounded-lg group">
          <span class="relative z-10">About</span>
          <div class="absolute inset-0 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="absolute bottom-0 left-1/2 w-0 h-0.5 bg-primary-500 group-hover:w-full group-hover:left-0 transition-all duration-300"></div>
        </a>
        <a routerLink="/contact" routerLinkActive="text-primary-600 bg-primary-50"
          class="relative px-4 py-2 text-gray-700 hover:text-primary-600 transition-all duration-300 font-medium rounded-lg group">
          <span class="relative z-10">Contact</span>
          <div class="absolute inset-0 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="absolute bottom-0 left-1/2 w-0 h-0.5 bg-primary-500 group-hover:w-full group-hover:left-0 transition-all duration-300"></div>
        </a>

        <!-- Enhanced Cart Icon with Artistic Design -->
        <div class="ml-6 pl-6 border-l border-gray-200">
          <a routerLink="/cart" class="relative group">
            <div class="relative p-3 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full shadow-lg transform transition-all duration-300 group-hover:scale-110 group-hover:shadow-xl">
              <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"/>
              </svg>
              <!-- Decorative Ring -->
              <div class="absolute -inset-1 border-2 border-primary-300 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <!-- Enhanced Cart Count Badge -->
            <span *ngIf="cartItemCount > 0"
                  class="absolute -top-2 -right-2 bg-gradient-to-r from-accent-500 to-accent-600 text-white text-xs font-bold rounded-full h-6 w-6 flex items-center justify-center shadow-lg animate-pulse-slow">
              {{cartItemCount}}
            </span>
          </a>
        </div>
      </nav>

      <!-- Enhanced Mobile menu button and cart -->
      <div class="md:hidden flex items-center space-x-3">
        <!-- Enhanced Mobile Cart Icon -->
        <a routerLink="/cart" class="relative group">
          <div class="relative p-2 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg shadow-md transform transition-all duration-300 group-hover:scale-110">
            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"/>
            </svg>
          </div>
          <span *ngIf="cartItemCount > 0"
                class="absolute -top-1 -right-1 bg-gradient-to-r from-accent-500 to-accent-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center shadow-lg">
            {{cartItemCount}}
          </span>
        </a>

        <!-- Enhanced Mobile Menu Button -->
        <button type="button" (click)="toggleMenu()" class="relative p-2 bg-gray-100 hover:bg-primary-50 rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50">
          <svg class="h-6 w-6 text-gray-700 hover:text-primary-600 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path *ngIf="!isMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            <path *ngIf="isMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Enhanced Mobile Navigation -->
    <div *ngIf="isMenuOpen" class="md:hidden py-6 border-t border-primary-200 bg-gradient-to-r from-primary-50/50 to-secondary-50/50 backdrop-blur-sm">
      <nav class="flex flex-col space-y-2">
        <a routerLink="/" routerLinkActive="text-primary-600 bg-primary-100" [routerLinkActiveOptions]="{exact: true}"
          class="relative px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-primary-50 transition-all duration-300 rounded-lg group">
          <span class="relative z-10 font-medium">Home</span>
          <div class="absolute left-0 top-1/2 w-0 h-0.5 bg-primary-500 group-hover:w-full transition-all duration-300 -translate-y-1/2"></div>
        </a>
        <a routerLink="/products" routerLinkActive="text-primary-600 bg-primary-100"
          class="relative px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-primary-50 transition-all duration-300 rounded-lg group">
          <span class="relative z-10 font-medium">Products</span>
          <div class="absolute left-0 top-1/2 w-0 h-0.5 bg-primary-500 group-hover:w-full transition-all duration-300 -translate-y-1/2"></div>
        </a>
        <a routerLink="/gallery" routerLinkActive="text-primary-600 bg-primary-100"
          class="relative px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-primary-50 transition-all duration-300 rounded-lg group">
          <span class="relative z-10 font-medium">Gallery</span>
          <div class="absolute left-0 top-1/2 w-0 h-0.5 bg-primary-500 group-hover:w-full transition-all duration-300 -translate-y-1/2"></div>
        </a>
        <a routerLink="/artists" routerLinkActive="text-primary-600 bg-primary-100"
          class="relative px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-primary-50 transition-all duration-300 rounded-lg group">
          <span class="relative z-10 font-medium">Artists</span>
          <div class="absolute left-0 top-1/2 w-0 h-0.5 bg-primary-500 group-hover:w-full transition-all duration-300 -translate-y-1/2"></div>
        </a>
        <a routerLink="/about" routerLinkActive="text-primary-600 bg-primary-100"
          class="relative px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-primary-50 transition-all duration-300 rounded-lg group">
          <span class="relative z-10 font-medium">About</span>
          <div class="absolute left-0 top-1/2 w-0 h-0.5 bg-primary-500 group-hover:w-full transition-all duration-300 -translate-y-1/2"></div>
        </a>
        <a routerLink="/contact" routerLinkActive="text-primary-600 bg-primary-100"
          class="relative px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-primary-50 transition-all duration-300 rounded-lg group">
          <span class="relative z-10 font-medium">Contact</span>
          <div class="absolute left-0 top-1/2 w-0 h-0.5 bg-primary-500 group-hover:w-full transition-all duration-300 -translate-y-1/2"></div>
        </a>
      </nav>
    </div>
  </div>
</header>
