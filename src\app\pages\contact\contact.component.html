<!-- Enhanced <PERSON> with Stunning <PERSON><PERSON>la Art Elements -->
<div class="relative h-96 overflow-hidden">
  <!-- Multi-layered Background -->
  <div class="absolute inset-0">
    <!-- Primary Background -->
    <div class="absolute inset-0 bg-gradient-to-br from-primary-600 via-primary-500 to-secondary-600"></div>

    <!-- Artistic Pattern Overlay -->
    <div class="absolute inset-0 mithila-fish-pattern opacity-20"></div>

    <!-- Animated Gradient -->
    <div class="absolute inset-0 bg-gradient-to-r from-primary-600/80 via-transparent to-accent-600/80 animate-gradient-shift"></div>
  </div>

  <!-- Floating Artistic Elements -->
  <div class="absolute inset-0 overflow-hidden opacity-30">
    <!-- Lotus Element -->
    <div class="absolute w-32 h-32 top-[20%] left-[15%] animate-float-slow">
      <svg viewBox="0 0 100 100" class="w-full h-full text-white/40">
        <circle cx="50" cy="50" r="30" fill="none" stroke="currentColor" stroke-width="2"/>
        <path d="M20 50 Q50 20 80 50 Q50 80 20 50" fill="currentColor" opacity="0.2"/>
      </svg>
    </div>

    <!-- Decorative Border Element -->
    <div class="absolute w-24 h-24 bottom-[25%] right-[20%] animate-float-medium">
      <svg viewBox="0 0 100 100" class="w-full h-full text-secondary-300/50">
        <rect x="20" y="20" width="60" height="60" fill="none" stroke="currentColor" stroke-width="2"/>
        <circle cx="50" cy="50" r="20" fill="none" stroke="currentColor" stroke-width="1"/>
      </svg>
    </div>
  </div>

  <!-- Hero Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <div class="max-w-4xl mx-auto">
      <!-- Enhanced Title -->
      <h1 class="text-5xl md:text-6xl font-bold text-white mb-6 font-display animate-fade-in-up">
        Contact Us
      </h1>

      <!-- Decorative Divider -->
      <div class="flex justify-center items-center space-x-4 mb-6">
        <div class="h-px bg-gradient-to-r from-transparent via-white/60 to-transparent w-20"></div>
        <div class="w-3 h-3 bg-secondary-400 rounded-full animate-pulse"></div>
        <div class="h-px bg-gradient-to-r from-transparent via-white/60 to-transparent w-20"></div>
      </div>

      <!-- Enhanced Subtitle -->
      <p class="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed animate-fade-in-up animation-delay-300 font-elegant">
        Get in touch with us for inquiries, collaborations, or to visit our gallery and experience the beauty of Mithila art
      </p>
    </div>
  </div>

  <!-- Artistic Bottom Border -->
  <div class="absolute bottom-0 left-0 right-0 h-2 bg-gradient-to-r from-primary-600 via-secondary-500 to-accent-600"></div>
</div>

<!-- Contact Section -->
<app-mithila-section
  primaryColor="#008C8C"
  secondaryColor="#D81B60"
  backgroundGradient="from-peacock-50 via-background-light to-magenta-50"
  backgroundOpacity="15"
  padding="py-16"
  [showDecorativeElements]="true"
>
  <div class="container">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
      <!-- Enhanced Contact Form -->
      <div class="mithila-art-card group">
        <!-- Enhanced Decorative Background -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary-50/0 via-primary-50/60 to-secondary-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1200"></div>

        <!-- Corner Decorations -->
        <div class="mithila-corner-decoration"></div>

        <!-- Enhanced Section Title -->
        <div class="mb-8">
          <h2 class="text-3xl font-display font-bold text-gray-900 mb-4 bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
            Send Us a Message
          </h2>
          <p class="text-gray-600 leading-relaxed">
            Fill out the form below and we'll get back to you as soon as possible. We'd love to hear from you!
          </p>
          <!-- Decorative Line -->
          <div class="mt-4 w-20 h-1 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full"></div>
        </div>

        <form (ngSubmit)="submitForm()" class="space-y-8 relative z-10" @slideUp>
          <!-- Enhanced Form Fields -->
          <div class="group">
            <label for="name" class="block text-sm font-semibold text-gray-700 mb-3 flex items-center">
              <span class="w-2 h-2 bg-primary-500 rounded-full mr-2"></span>
              Full Name
            </label>
            <div class="relative">
              <input
                type="text"
                id="name"
                name="name"
                [(ngModel)]="contactForm.name"
                required
                class="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 bg-white/80 backdrop-blur-sm group-hover:border-primary-300"
                placeholder="Enter your full name"
              >
              <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
              </div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="group">
              <label for="email" class="block text-sm font-semibold text-gray-700 mb-3 flex items-center">
                <span class="w-2 h-2 bg-secondary-500 rounded-full mr-2"></span>
                Email Address
              </label>
              <div class="relative">
                <input
                  type="email"
                  id="email"
                  name="email"
                  [(ngModel)]="contactForm.email"
                  required
                  class="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:border-secondary-500 transition-all duration-300 bg-white/80 backdrop-blur-sm group-hover:border-secondary-300"
                  placeholder="Enter your email address"
                >
                <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                  <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                  </svg>
                </div>
              </div>
            </div>
            <div class="group">
              <label for="phone" class="block text-sm font-semibold text-gray-700 mb-3 flex items-center">
                <span class="w-2 h-2 bg-accent-500 rounded-full mr-2"></span>
                Phone Number
                <span class="text-xs text-gray-500 ml-2">(optional)</span>
              </label>
              <div class="relative">
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  [(ngModel)]="contactForm.phone"
                  class="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-300 bg-white/80 backdrop-blur-sm group-hover:border-accent-300"
                  placeholder="Enter your phone number"
                >
                <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                  <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <div class="group">
            <label for="subject" class="block text-sm font-semibold text-gray-700 mb-3 flex items-center">
              <span class="w-2 h-2 bg-success-500 rounded-full mr-2"></span>
              Subject
            </label>
            <div class="relative">
              <input
                type="text"
                id="subject"
                name="subject"
                [(ngModel)]="contactForm.subject"
                required
                class="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-success-500 focus:border-success-500 transition-all duration-300 bg-white/80 backdrop-blur-sm group-hover:border-success-300"
                placeholder="What is this regarding?"
              >
              <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"/>
                </svg>
              </div>
            </div>
          </div>

          <div class="group">
            <label for="message" class="block text-sm font-semibold text-gray-700 mb-3 flex items-center">
              <span class="w-2 h-2 bg-primary-500 rounded-full mr-2"></span>
              Message
            </label>
            <div class="relative">
              <textarea
                id="message"
                name="message"
                [(ngModel)]="contactForm.message"
                required
                rows="6"
                class="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 bg-white/80 backdrop-blur-sm group-hover:border-primary-300 resize-none"
                placeholder="Tell us about your inquiry, collaboration ideas, or any questions you have..."
              ></textarea>
              <div class="absolute top-4 right-4">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
              </div>
            </div>
          </div>

          <!-- Enhanced Submit Button -->
          <div class="pt-4">
            <button type="submit" class="btn-mithila-primary w-full md:w-auto px-12 py-4 text-lg font-semibold">
              <span class="flex items-center justify-center">
                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                </svg>
                Send Message
              </span>
            </button>
          </div>
        </form>

        <!-- Decorative Element -->
        <app-mithila-decorative-element
          [primaryColor]="'#D81B60'"
          [secondaryColor]="'#008C8C'"
          [type]="'lotus'"
          position="absolute -bottom-4 -right-4"
          classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"
          size="40px">
        </app-mithila-decorative-element>
      </div>

      <!-- Contact Information -->
      <div class="bg-white/90 backdrop-blur-sm rounded-lg shadow-md p-6 relative overflow-hidden group">
        <!-- Decorative Background -->
        <div class="absolute inset-0 bg-gradient-to-br from-peacock-50/0 via-peacock-50/50 to-peacock-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

        <app-section-title
          title="Contact Information"
          subtitle="Visit us or reach out through any of the following channels."
          alignment="left"
        ></app-section-title>

        <div class="mt-8 space-y-8 relative z-10" @staggerIn>
          <!-- Address -->
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-100 text-primary-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Our Location</h3>
              <p class="mt-1 text-gray-600">Barahbigha, Janaki Mandir Marg, Janakpurdham-08, Dhanusha, Nepal</p>
              <p class="mt-2 text-sm text-primary-600">
                <a href="https://maps.google.com" target="_blank" class="flex items-center">
                  Get Directions
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </p>
            </div>
          </div>

          <!-- Phone -->
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-100 text-primary-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Phone Numbers</h3>
              <p class="mt-1 text-gray-600">+977-9814830580 / +977-9821762884</p>
              <p class="mt-2 text-sm text-primary-600">
                <a href="tel:+9779814830580" class="flex items-center">
                  Call Us
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </p>
            </div>
          </div>

          <!-- Email -->
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-100 text-primary-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Email</h3>
              <p class="mt-1 text-gray-600">mithilanighar&#64;gmail.com</p>
              <p class="mt-2 text-sm text-primary-600">
                <a href="mailto:<EMAIL>" class="flex items-center">
                  Send Email
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </p>
            </div>
          </div>

          <!-- Hours -->
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-100 text-primary-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Opening Hours</h3>
              <p class="mt-1 text-gray-600">Open daily: 9:00 AM - 8:00 PM</p>
              <p class="mt-1 text-gray-600">We are open all days of the week including holidays.</p>
            </div>
          </div>
        </div>

        <!-- Social Media Links -->
        <div class="mt-10">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Connect With Us</h3>
          <div class="flex space-x-4">
            <a *ngFor="let social of socialLinks"
               [href]="social.url"
               target="_blank"
               [ngClass]="social.color"
               class="flex items-center justify-center h-10 w-10 rounded-full text-white transition-transform duration-300 hover:scale-110">
              <i [class]="social.icon"></i>
            </a>
          </div>
        </div>

        <!-- Decorative Element -->
        <app-mithila-decorative-element
          [primaryColor]="'#008C8C'"
          [secondaryColor]="'#D81B60'"
          [type]="'peacock'"
          position="absolute -bottom-4 -right-4"
          classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"
          size="40px">
        </app-mithila-decorative-element>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Map Section with Mithila Art Elements -->
<section class="relative overflow-hidden">
  <div class="h-96 w-full">
    <iframe
      src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3571.5668128441087!2d85.92093!3d26.7271!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39ec4144bd00f9b3%3A0x4e9fe97a37b9c128!2sJanaki%20Mandir!5e0!3m2!1sen!2sus!4v1651234567890!5m2!1sen!2sus"
      width="100%"
      height="100%"
      style="border:0;"
      allowfullscreen=""
      loading="lazy"
      referrerpolicy="no-referrer-when-downgrade">
    </iframe>
  </div>

  <!-- Decorative Border -->
  <app-mithila-border
    [primaryColor]="'#F4B400'"
    [secondaryColor]="'#C1440E'"
    [position]="'top'"
    [height]="'30px'"
  ></app-mithila-border>
</section>

<!-- FAQ Section -->
<app-mithila-section
  primaryColor="#3B945E"
  secondaryColor="#F7A700"
  backgroundGradient="from-success-50 via-background-light to-secondary-50"
  backgroundOpacity="15"
  padding="py-16"
  [showDecorativeElements]="true"
>
  <app-section-title
    title="Frequently Asked Questions"
    subtitle="Find answers to common questions about our gallery and services"
  ></app-section-title>

  <div class="max-w-3xl mx-auto mt-12">
    <div class="space-y-6" @staggerIn>
      <div *ngFor="let faq of faqItems; let i = index"
           class="bg-white/80 backdrop-blur-sm rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900">{{faq.question}}</h3>
          <p class="mt-2 text-gray-600">{{faq.answer}}</p>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Call to Action -->
<app-mithila-section
  primaryColor="#264653"
  secondaryColor="#C1440E"
  backgroundGradient="from-accent-50 via-background-light to-primary-50"
  backgroundOpacity="15"
  padding="py-16"
  [showDecorativeElements]="true"
>
  <div class="max-w-4xl mx-auto text-center">
    <h2 class="text-3xl font-bold text-gray-900 mb-4">Visit Our Gallery</h2>
    <p class="text-xl text-gray-600 mb-8">Experience the rich tradition of Mithila art in person at our gallery in Janakpurdham.</p>
    <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
      <a href="tel:+9779814830580" class="btn btn-primary">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
        </svg>
        Call Us
      </a>
      <a href="mailto:<EMAIL>" class="btn btn-secondary">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
        Email Us
      </a>
    </div>
  </div>
</app-mithila-section>
