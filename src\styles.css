/* <PERSON><PERSON><PERSON> - Global Styles */
/* Traditional Mithila Art Inspired Design */

/* Enhanced Custom Fonts for Artistic Appeal */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Merriweather:wght@400;700&family=Playfair+Display:wght@400;500;600;700;800;900&family=Cinzel:wght@400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&family=Lato:wght@300;400;700&family=Caveat:wght@400;600;700&family=Dancing+Script:wght@400;500;600;700&family=Crimson+Text:wght@400;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced Base Styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply font-body text-background-dark overflow-x-hidden;
    background: linear-gradient(135deg, #FAF8F1 0%, #F5F3EC 100%);
    min-height: 100vh;
  }

  /* Beautiful Text Selection */
  ::selection {
    background: rgba(193, 68, 14, 0.2);
    color: #C1440E;
  }

  ::-moz-selection {
    background: rgba(193, 68, 14, 0.2);
    color: #C1440E;
  }

  /* Enhanced Scrollbar */
  ::-webkit-scrollbar {
    width: 12px;
  }

  ::-webkit-scrollbar-track {
    background: linear-gradient(to bottom, #FAF8F1, #F0EDE4);
    border-radius: 6px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #C1440E, #9A360B);
    border-radius: 6px;
    border: 2px solid #FAF8F1;
    box-shadow: inset 0 0 3px rgba(0,0,0,0.1);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #F4B400, #C39000);
    box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-semibold;
  }
  h1 {
    @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl;
  }
  h2 {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl;
  }
  h3 {
    @apply text-xl sm:text-2xl md:text-3xl;
  }
  h4 {
    @apply text-lg sm:text-xl md:text-2xl;
  }
  h5 {
    @apply text-base sm:text-lg md:text-xl;
  }
  h6 {
    @apply text-sm sm:text-base md:text-lg;
  }
  a {
    @apply text-primary-500 hover:text-primary-600 transition-colors duration-300;
  }

  /* Responsive container padding */
  .container {
    @apply px-4 sm:px-6 lg:px-8;
  }
}

@layer components {
  .btn {
    @apply px-5 py-3 rounded-md font-medium transition-all duration-300 transform hover:-translate-y-1 hover:shadow-md;
  }
  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600;
  }
  .btn-secondary {
    @apply bg-secondary-500 text-background-dark hover:bg-secondary-600;
  }
  .btn-accent {
    @apply bg-accent-500 text-white hover:bg-accent-600;
  }
  .btn-success {
    @apply bg-success-500 text-white hover:bg-success-600;
  }
  .btn-outline {
    @apply border-2 border-primary-500 text-primary-500 hover:bg-primary-50;
  }
  .btn-outline-secondary {
    @apply border-2 border-secondary-500 text-secondary-600 hover:bg-secondary-50;
  }
  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300;
  }
  .input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  .section {
    @apply py-16 md:py-20 lg:py-24;
  }
  .section-alt {
    @apply py-16 md:py-20 lg:py-24 bg-primary-50;
  }
  .container {
    @apply mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }
  .heading-decorated {
    @apply relative inline-block;
  }
  .heading-decorated::after {
    @apply content-[''] absolute -bottom-2 left-0 w-1/3 h-1 bg-primary-500;
  }
  .mithila-card {
    @apply relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1;
  }
}

/* Mithila Art Specific Styles */
.mithila-border {
  @apply border-4 border-mithila-red p-1 relative;
  background-image: repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(244, 180, 0, 0.15) 10px, rgba(244, 180, 0, 0.15) 20px);
}

.mithila-border::before {
  @apply content-[''] absolute -top-2 -left-2 w-4 h-4 border-t-4 border-l-4 border-mithila-blue;
}

.mithila-border::after {
  @apply content-[''] absolute -bottom-2 -right-2 w-4 h-4 border-b-4 border-r-4 border-mithila-blue;
}

.mithila-card {
  @apply card p-6 border-t-4 border-mithila-red;
}

.mithila-heading {
  @apply font-display text-3xl md:text-4xl lg:text-5xl text-mithila-red leading-tight;
}

.mithila-pattern-bg {
  background-color: #FAF8F1;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23C1440E' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.mithila-divider {
  @apply relative h-4 my-8;
}

.mithila-divider::before {
  @apply content-[''] absolute top-1/2 left-0 w-full h-0.5 bg-mithila-red bg-opacity-20;
}

.mithila-divider::after {
  @apply content-[''] absolute top-1/2 left-1/2 w-16 h-0.5 bg-mithila-red -translate-x-1/2 -translate-y-1/2;
}

/* Beautiful Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes floatSlow {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes floatMedium {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes floatFast {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-25px);
  }
}

@keyframes spinSlow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Animation Classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-slow {
  animation: floatSlow 8s ease-in-out infinite;
}

.animate-float-medium {
  animation: floatMedium 7s ease-in-out infinite;
}

.animate-float-fast {
  animation: floatFast 5s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spinSlow 20s linear infinite;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

/* Animation Delays */
.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-600 {
  animation-delay: 600ms;
}

.animation-delay-900 {
  animation-delay: 900ms;
}

/* Hover Effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(193, 68, 14, 0.3);
}

/* Interactive Elements */
.interactive-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.interactive-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Gradient Animations */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradientShift 4s ease infinite;
}

/* Enhanced Floating Design System Animations */
@keyframes gradient-background {
  0%, 100% {
    background-position: 0% 50%;
    opacity: 0.7;
  }
  50% {
    background-position: 100% 50%;
    opacity: 0.9;
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 0%;
    opacity: 0.3;
  }
  50% {
    background-position: 100% 100%;
    opacity: 0.6;
  }
}

@keyframes ping-slow {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes border-glow {
  0%, 100% {
    opacity: 0.5;
    filter: drop-shadow(0 0 5px rgba(255,255,255,0.3));
  }
  50% {
    opacity: 0.8;
    filter: drop-shadow(0 0 15px rgba(255,255,255,0.5));
  }
}

@keyframes fade-in-delay-1 {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes fade-slide-in {
  0% { opacity: 0; transform: translateY(30px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes fade-slide-in-left {
  0% { opacity: 0; transform: translateX(-30px); }
  100% { opacity: 1; transform: translateX(0); }
}

@keyframes fade-slide-in-right {
  0% { opacity: 0; transform: translateX(30px); }
  100% { opacity: 1; transform: translateX(0); }
}

@keyframes stagger-in {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

/* Enhanced Animation Classes */
.animate-gradient-background {
  animation: gradient-background 8s ease-in-out infinite;
  background-size: 200% 200%;
}

.animate-gradient-shift {
  animation: gradient-shift 12s ease-in-out infinite;
  background-size: 200% 200%;
}

.animate-ping-slow {
  animation: ping-slow 3s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-border-glow {
  animation: border-glow 5s ease-in-out infinite;
}

.animate-fade-in-delay-1 {
  animation: fade-in-delay-1 1s ease-out 1s both;
}

.animate-fade-slide-in {
  animation: fade-slide-in 0.8s ease-out;
}

.animate-fade-slide-in-left {
  animation: fade-slide-in-left 0.8s ease-out;
}

.animate-fade-slide-in-right {
  animation: fade-slide-in-right 0.8s ease-out;
}

.animate-stagger-in {
  animation: stagger-in 0.6s ease-out;
}

/* Gradient Utilities */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Responsive Floating Elements */
@media (max-width: 768px) {
  .mithila-floating-section .absolute {
    transform: scale(0.7);
  }

  .animate-float-slow,
  .animate-float-medium,
  .animate-float-fast {
    animation-duration: 4s;
  }
}

/* Enhanced Interactive Effects */
.mithila-section-hover:hover .animate-float-slow {
  animation-duration: 4s;
}

.mithila-section-hover:hover .animate-float-medium {
  animation-duration: 3s;
}

.mithila-section-hover:hover .animate-float-fast {
  animation-duration: 2s;
}

/* Backdrop Blur Utilities */
.backdrop-blur-xs {
  backdrop-filter: blur(1px);
}

.backdrop-blur-sm {
  backdrop-filter: blur(2px);
}

/* Enhanced Card Styles for Floating Design */
.mithila-floating-card {
  @apply relative overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.mithila-floating-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(193, 68, 14, 0.05), rgba(244, 180, 0, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.mithila-floating-card:hover::before {
  opacity: 1;
}

/* Enhanced Mithila Art Patterns and Backgrounds */
.mithila-lotus-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23C1440E' fill-opacity='0.08'%3E%3Cpath d='M40 20c-11 0-20 9-20 20s9 20 20 20 20-9 20-20-9-20-20-20zm0 35c-8.3 0-15-6.7-15-15s6.7-15 15-15 15 6.7 15 15-6.7 15-15 15z'/%3E%3Cpath d='M40 25c-8.3 0-15 6.7-15 15s6.7 15 15 15 15-6.7 15-15-6.7-15-15-15zm0 25c-5.5 0-10-4.5-10-10s4.5-10 10-10 10 4.5 10 10-4.5 10-10 10z'/%3E%3C/g%3E%3C/svg%3E");
}

.mithila-peacock-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23F4B400' fill-opacity='0.06'%3E%3Cpath d='M50 10c-5 0-10 5-10 10v20c0 5 5 10 10 10s10-5 10-10V20c0-5-5-10-10-10zm0 35c-2.8 0-5-2.2-5-5V20c0-2.8 2.2-5 5-5s5 2.2 5 5v20c0 2.8-2.2 5-5 5z'/%3E%3Cpath d='M30 50c0-11 9-20 20-20s20 9 20 20-9 20-20 20-20-9-20-20zm35 0c0-8.3-6.7-15-15-15s-15 6.7-15 15 6.7 15 15 15 15-6.7 15-15z'/%3E%3C/g%3E%3C/svg%3E");
}

.mithila-fish-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='120' height='60' viewBox='0 0 120 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23008C8C' fill-opacity='0.07'%3E%3Cpath d='M20 30c0-8 6-15 15-15h50c8 0 15 7 15 15s-7 15-15 15H35c-9 0-15-7-15-15zm70 0c0-5.5-4.5-10-10-10H35c-5.5 0-10 4.5-10 10s4.5 10 10 10h45c5.5 0 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E");
}

/* Beautiful Gradient Backgrounds */
.mithila-gradient-warm {
  background: linear-gradient(135deg, #FCE9E1 0%, #F9D3C3 25%, #F5A786 50%, #F07B49 75%, #EB5A1C 100%);
}

.mithila-gradient-cool {
  background: linear-gradient(135deg, #E0F5F5 0%, #C1EBEB 25%, #83D6D6 50%, #45C2C2 75%, #16ADAD 100%);
}

.mithila-gradient-sunset {
  background: linear-gradient(135deg, #FEF9E7 0%, #FCF3CF 20%, #FAE69F 40%, #F7DA6F 60%, #F5CD3F 80%, #F4B400 100%);
}

.mithila-gradient-royal {
  background: linear-gradient(135deg, #C1440E 0%, #F4B400 50%, #008C8C 100%);
}

/* Enhanced Typography Styles */
.mithila-title {
  @apply font-display text-4xl md:text-5xl lg:text-6xl font-bold;
  background: linear-gradient(135deg, #C1440E, #F4B400, #008C8C);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.mithila-subtitle {
  @apply font-serif text-xl md:text-2xl lg:text-3xl text-gray-700;
  font-style: italic;
  letter-spacing: 0.5px;
}

.mithila-text-artistic {
  @apply font-handwritten text-lg md:text-xl text-primary-600;
  line-height: 1.8;
}

/* Enhanced Button Styles */
.btn-mithila-primary {
  @apply px-8 py-4 rounded-full font-semibold text-white transition-all duration-300 transform hover:scale-105;
  background: linear-gradient(135deg, #C1440E, #F4B400);
  box-shadow: 0 4px 15px rgba(193, 68, 14, 0.3);
  border: 2px solid transparent;
}

.btn-mithila-primary:hover {
  background: linear-gradient(135deg, #F4B400, #C1440E);
  box-shadow: 0 8px 25px rgba(193, 68, 14, 0.4);
  transform: translateY(-2px) scale(1.05);
}

.btn-mithila-outline {
  @apply px-8 py-4 rounded-full font-semibold transition-all duration-300 transform hover:scale-105;
  background: transparent;
  border: 2px solid #C1440E;
  color: #C1440E;
  position: relative;
  overflow: hidden;
}

.btn-mithila-outline::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #C1440E, #F4B400);
  transition: left 0.3s ease;
  z-index: -1;
}

.btn-mithila-outline:hover::before {
  left: 0;
}

.btn-mithila-outline:hover {
  color: white;
  border-color: #F4B400;
  transform: translateY(-2px) scale(1.05);
}

/* Enhanced Card Designs */
.mithila-art-card {
  @apply relative overflow-hidden rounded-2xl shadow-xl transition-all duration-500 transform hover:-translate-y-3;
  background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(250,248,241,0.9));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(193, 68, 14, 0.1);
}

.mithila-art-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #C1440E, #F4B400, #008C8C, #3B945E);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mithila-art-card:hover::before {
  opacity: 1;
}

.mithila-art-card:hover {
  box-shadow: 0 20px 40px rgba(193, 68, 14, 0.2);
}

/* Decorative Elements */
.mithila-corner-decoration {
  position: relative;
}

.mithila-corner-decoration::before,
.mithila-corner-decoration::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid #C1440E;
}

.mithila-corner-decoration::before {
  top: -3px;
  left: -3px;
  border-right: none;
  border-bottom: none;
}

.mithila-corner-decoration::after {
  bottom: -3px;
  right: -3px;
  border-left: none;
  border-top: none;
}

/* Enhanced Animations for Artistic Elements */
@keyframes gradient-background {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes gradient-shift {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

@keyframes float-slow {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes float-medium {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(-3deg); }
}

@keyframes float-fast {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(2deg); }
}

@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes reverse-spin-slow {
  from { transform: rotate(360deg); }
  to { transform: rotate(0deg); }
}

@keyframes pulse-slow {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation Classes */
.animate-gradient-background {
  background-size: 400% 400%;
  animation: gradient-background 8s ease infinite;
}

.animate-gradient-shift {
  animation: gradient-shift 6s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 6s ease-in-out infinite;
}

.animate-float-medium {
  animation: float-medium 4s ease-in-out infinite;
}

.animate-float-fast {
  animation: float-fast 3s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

.animate-reverse-spin-slow {
  animation: reverse-spin-slow 15s linear infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 3s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

/* Animation Delays */
.animation-delay-300 {
  animation-delay: 0.3s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

.animation-delay-900 {
  animation-delay: 0.9s;
}

.animation-delay-1200 {
  animation-delay: 1.2s;
}

/* Hover Effects for Interactive Elements */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(193, 68, 14, 0.3);
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .mithila-title {
    @apply text-3xl md:text-4xl;
  }

  .mithila-subtitle {
    @apply text-lg md:text-xl;
  }

  .btn-mithila-primary,
  .btn-mithila-outline {
    @apply px-6 py-3 text-sm;
  }
}

/* Print Styles */
@media print {
  .animate-float-slow,
  .animate-float-medium,
  .animate-float-fast,
  .animate-spin-slow,
  .animate-reverse-spin-slow,
  .animate-pulse-slow {
    animation: none !important;
  }

  .mithila-art-card::before,
  .mithila-floating-card::before {
    display: none !important;
  }
}
